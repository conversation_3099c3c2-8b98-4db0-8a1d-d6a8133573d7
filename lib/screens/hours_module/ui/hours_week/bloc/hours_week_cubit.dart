import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursweek_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/repository/hours_week_repository.dart';

part 'hours_week_state.dart';

class HoursWeekCubit extends Cubit<HoursWeekState> {
  HoursWeekCubit() : super(HoursWeekInitial());

  //hoursweek list
  List<HoursWeekResponseModel> hoursWeekList = [];
  Map<String, List<HoursWeekResponseModel>> allHoursWeekList = {};
  ValueNotifier<bool> isLoading = ValueNotifier(true);

  Future<void> hoursWeekApiCall({
    required BuildContext context,
    required String isoMonth,
    required String monthYearName,
    bool refresh = false,
  }) async {
    print("monthYearName: $monthYearName");
    print(!allHoursWeekList.containsKey(monthYearName));
    print(allHoursWeekList);

    if (refresh || !allHoursWeekList.containsKey(monthYearName)) {
      isLoading.value = true;
      print("api started =====>");
      final HoursWeekApiRepository hoursWeekApiRepository =
          HoursWeekApiRepository();
      final response = await hoursWeekApiRepository.hoursWeekApi(
          context: context, isoMonth: isoMonth);
      print("api done =====>${response}");

      if (response!.isNotEmpty) {
        hoursWeekList.clear();
        hoursWeekList.addAll(response);
        allHoursWeekList[monthYearName] = response;
        // if (!allHoursWeekList.containsKey(monthYearName)) {
        //   allHoursWeekList.addAll({"$monthYearName": response});
        // }
        print(allHoursWeekList);
      } else {
        // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
      }
      isLoading.value = false;
      emit(HoursWeekInitial());
    }
  }

  List<String> dayList = [];

  List<String> getDayNameFromWeekData(List<String> weekNameList, int group,
      Map<String, List<String>> weekData) {
    print("datatatatta" + weekNameList[group]);
    dayList = weekData[weekNameList[group]]!;

    List<String> reversedDayList = dayList.reversed.toList();
    print("dayList $reversedDayList");
    return reversedDayList;
  }

  String calculateTotalHours(
      {required DateTime date,
      required String timeFrom,
      required String timeUntil,
      required String breakTime}) {
    // Check if both timeFrom and timeUntil are valid
    if (timeFrom == "00:00" || timeUntil == "00:00") {
      return "00:00";
    }

    if (timeUntil.isEmpty || timeUntil == "" || timeUntil == "00:00") {
      return "00:00";
    }

    if (timeFrom.isEmpty || timeFrom == "" || timeFrom == "00:00") {
      return "00:00";
    }

    if (breakTime.isEmpty || breakTime == "" || breakTime == "00:00") {
      breakTime = "00:00";
    }

    try {
      // Parse break time parts
      final breakTimeParts = breakTime.split(":");

      // Create DateTime objects for start and end times on the same date
      DateTime startDateTime = DateTime.parse(
          "${date.year}-${DateFormatFunctions.formatDay(date.month)}-${DateFormatFunctions.formatDay(date.day)} $timeFrom:00");
      DateTime endDateTime = DateTime.parse(
          "${date.year}-${DateFormatFunctions.formatDay(date.month)}-${DateFormatFunctions.formatDay(date.day)} $timeUntil:00");

      // Handle case where end time is before start time (next day)
      if (endDateTime.isBefore(startDateTime)) {
        endDateTime = endDateTime.add(Duration(days: 1));
      }

      // Calculate the difference between start and end times
      Duration workDuration = endDateTime.difference(startDateTime);

      // Subtract break time
      int breakHours = int.parse(breakTimeParts[0]);
      int breakMinutes = int.parse(breakTimeParts[1]);
      Duration breakDuration =
          Duration(hours: breakHours, minutes: breakMinutes);

      Duration totalWorkDuration = workDuration - breakDuration;

      // Ensure we don't have negative time
      if (totalWorkDuration.isNegative) {
        return "00:00";
      }

      // Format the result as "HH:mm"
      int totalHours = totalWorkDuration.inHours;
      int totalMinutes = totalWorkDuration.inMinutes.remainder(60);

      String formattedHours = totalHours.toString().padLeft(2, '0');
      String formattedMinutes = totalMinutes.toString().padLeft(2, '0');

      return '$formattedHours:$formattedMinutes';
    } catch (e) {
      print("Error calculating total hours: $e");
      return "00:00";
    }
  }

  String addTimes(String weekHours, String totalDayTime) {
    if (weekHours.isEmpty || weekHours == "00:00") {
      weekHours = "00:00";
    }

    if (totalDayTime.isEmpty || totalDayTime == "00:00") {
      totalDayTime = "00:00";
    }

    List<String> weekHoursParts = weekHours.split(":");
    List<String> totalDayTimeParts = totalDayTime.split(":");

    int hours = int.parse(weekHoursParts[0]) + int.parse(totalDayTimeParts[0]);
    int minutes =
        int.parse(weekHoursParts[1]) + int.parse(totalDayTimeParts[1]);

    if (minutes >= 60) {
      hours++;
      minutes = minutes - 60;
    }

    String totalWeeklyHours =
        '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';

    return totalWeeklyHours;
  }
}
